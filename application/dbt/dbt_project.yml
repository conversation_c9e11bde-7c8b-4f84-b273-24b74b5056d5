name: 'price_crawler_analytics'
version: '1.0.0'
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: 'price_crawler_analytics'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analysis"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]
docs-paths: ["docs", "models"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"
  - "dbt_modules"

vars:
  crawler_run_date: '1900-01-01'

# Configuring models
# Full documentation: https://docs.getdbt.com/reference/model-configs
models:
  price_crawler_analytics:
    staging:
      +materialized: table
      +file_format: parquet
      +partitioned_by: [ 'crawler_run_date' ]
      +table_type: iceberg
      +table_properties:
        optimize_rewrite_delete_file_threshold: '2'
        vacuum_max_snapshot_age_seconds: '604800000'
        vacuum_min_snapshots_to_keep: '2'
        optimize_rewrite_data_file_threshold: '5'
