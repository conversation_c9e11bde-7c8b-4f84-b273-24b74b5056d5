{{
  config(
    materialized='incremental',
    incremental_strategy='append',
    file_format='parquet',
    partitioned_by=['crawler_run_date'],
    table_type='iceberg',
    on_schema_change='append_new_columns',
    pre_hook="""
      {% if is_incremental() %}
        {{ delete_iceberg_partition(
          table_name=this,
          partition_column='crawler_run_date',
          partition_value=var('crawler_run_date')
        ) }}
      {% endif %}
    """
  )
}}

SELECT
    crawler_id,
    experiment_name,
    experiment_file_md5_hash,
    status,
    error,
    retry_attempts,
    json_format(json_extract(result_item, '$.ResultItemDetail'))                                    AS result_item_detail_json,
    json_extract_scalar(result_item, '$.ProductId')                                                 AS product_id,
    json_extract_scalar(result_item, '$.ProviderId')                                                AS provider_id,
    json_extract_scalar(result_item, '$.ProviderNameInfobase')                                      AS provider_name_infobase,
    json_extract_scalar(result_item, '$.ProductNameInfobase')                                       AS product_name_infobase,
    json_extract_scalar(result_item, '$.LogoName')                                                  AS logo_name,
    json_extract_scalar(result_item, '$.ComparisSatisfactionGrade')                                 AS comparis_satisfaction_grade,
    json_extract_scalar(result_item, '$.ComparisAward')                                             AS comparis_award,
    json_extract_scalar(result_item, '$.IsWinner')                                                  AS is_winner,
    json_extract_scalar(result_item, '$.OfferQuote')                                                AS offer_quote,
    json_extract_scalar(result_item, '$.OfferButtonType')                                           AS offer_button_type,
    json_extract_scalar(result_item, '$.FootnoteIndexValue')                                        AS footnote_index_value,
    json_extract_scalar(result_item, '$.SpecialOfferInfobase')                                      AS special_offer_infobase,
    json_extract_scalar(result_item, '$.PremiumFirstYear')                                          AS premium_first_year,
    json_extract_scalar(result_item, '$.PremiumFirstYearDiscount')                                  AS premium_first_year_discount,
    json_format(json_extract(result_item, '$.Footnotes'))                                           AS footnotes_json,
    json_extract_scalar(result_item, '$.BonusProtectionCoverageMark')                               AS bonus_protection_coverage_mark,
    json_extract_scalar(result_item, '$.ParkingDamagesCoverageMark')                                AS parking_damages_coverage_mark,
    json_extract_scalar(result_item, '$.GrossNegligenceCoverageMark')                               AS gross_negligence_coverage_mark,
    json_extract_scalar(result_item, '$.AssistanceCoverageMark')                                    AS assistance_coverage_mark,
    json_extract_scalar(result_item, '$.RetentionCoverageMark')                                     AS retention_coverage_mark,
    json_extract_scalar(result_item, '$.PersonalEffectsCoverageMark')                               AS personal_effects_coverage_mark,
    json_extract_scalar(result_item, '$.OccupantProtectionCoverageMark')                            AS occupant_protection_coverage_mark,
    json_format(json_extract(result_item, '$.EcommerceItemModel'))                                  AS ecommerce_item_model_json,
    json_extract_scalar(result_item, '$.ResultCategory')                                            AS result_category,
    json_extract_scalar(result_item, '$.IsDirectBuyAvailable')                                      AS is_direct_buy_available,
    json_extract_scalar(result_item, '$.HasMileagePricing')                                         AS has_mileage_pricing,
    json_extract_scalar(result_item, '$.IsTopBoxAd')                                                AS is_top_box_ad,
    json_format(json_extract(result_item, '$.Benefits'))                                            AS benefits_json,
    json_extract_scalar(result_item, '$.ResultItemDetail.LiabilityCoverPremium')                    AS liability_cover_premium,
    json_extract_scalar(result_item, '$.ResultItemDetail.LiabilityCoverDeductibleYoungDriver')      AS liability_cover_deductible_young_driver,
    json_extract_scalar(result_item, '$.ResultItemDetail.LiabilityCoverYoungDriverInfobaseKey')     AS liability_cover_young_driver_infobase_key,
    json_extract_scalar(result_item, '$.ResultItemDetail.LiabilityCoverDeductibleOtherDriver')      AS liability_cover_deductible_other_driver,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasLiabilityBonusCover')                   AS has_liability_bonus_cover,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasComprehensiveBonusCover')               AS has_comprehensive_bonus_cover,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasPartialCover')                          AS has_partial_cover,
    json_extract_scalar(result_item, '$.ResultItemDetail.PartialCoverPremium')                      AS partial_cover_premium,
    json_extract_scalar(result_item, '$.ResultItemDetail.PartialCoverDeductible')                   AS partial_cover_deductible,
    json_extract_scalar(result_item, '$.ResultItemDetail.RequestedPartialCoverDeductible')          AS requested_partial_cover_deductible,
    json_extract_scalar(result_item, '$.ResultItemDetail.IsRequestedPartialCoverDeductible')        AS is_requested_partial_cover_deductible,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasComprehensiveCover')                    AS has_comprehensive_cover,
    json_extract_scalar(result_item, '$.ResultItemDetail.ComprehensiveCoverPremium')                AS comprehensive_cover_premium,
    json_extract_scalar(result_item, '$.ResultItemDetail.ComprehensiveCoverDeductibleYoungDriver')  AS comprehensive_cover_deductible_young_driver,
    json_extract_scalar(result_item, '$.ResultItemDetail.ComprehensiveCoverYoungDriverInfobaseKey') AS comprehensive_cover_young_driver_infobase_key,
    json_extract_scalar(result_item, '$.ResultItemDetail.ComprehensiveCoverDeductibleOtherDriver')  AS comprehensive_cover_deductible_other_driver,
    json_extract_scalar(result_item, '$.ResultItemDetail.IsRequestedComprehensiveCoverDeductible')  AS is_requested_comprehensive_cover_deductible,
    json_extract_scalar(result_item, '$.ResultItemDetail.RequestedComprehensiveCoverDeductible')    AS requested_comprehensive_cover_deductible,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasPersonalEffects')                       AS has_personal_effects,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasParkingDamage')                         AS has_parking_damage,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasParkingDamageLimited')                  AS has_parking_damage_limited,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasGrossNegligence')                       AS has_gross_negligence,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasAssistance')                            AS has_assistance,
    json_extract_scalar(result_item, '$.ResultItemDetail.HasOccupantProtection')                    AS has_occupant_protection,
    json_extract_scalar(result_item, '$.ResultItemDetail.OccupantProtectionPremium')                AS occupant_protection_premium,
    json_extract_scalar(result_item, '$.ResultItemDetail.SatutoryCharges')                          AS satutory_charges,
    json_extract_scalar(results, '$.IsFinished')                                                    AS is_finished,
    '{{ var('iptiq_load_id', 'NULL') }}'                                                            AS iptiq_load_id,
    '{{ var('iptiq_load_date', 'NULL') }}'                                                          AS iptiq_load_date,
    '{{ var('iptiq_execution_date', 'NULL') }}'                                                     AS iptiq_execution_date,
    crawler_run_date                                                                                AS crawler_run_date
FROM {{ source('databucket_raw', 'comparis_motor_insurance_pricing_data') }}
CROSS JOIN UNNEST(
    CAST(json_parse(json_format(json_extract(results, '$.ResultList'))) AS ARRAY<JSON>)
) AS t(result_item)
WHERE
    crawler_run_date = CAST('{{ var('crawler_run_date', "'1900-01-01'") }}' AS DATE)
