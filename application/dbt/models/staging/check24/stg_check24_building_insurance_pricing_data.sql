{{
  config(
    materialized='incremental',
    incremental_strategy='append',
    file_format='parquet',
    partitioned_by=['crawler_run_date'],
    table_type='iceberg',
    on_schema_change='append_new_columns',
    pre_hook="""
      {% if is_incremental() %}
        {{ delete_iceberg_partition(
          table_name=this,
          partition_column='crawler_run_date',
          partition_value=var('crawler_run_date')
        ) }}
      {% endif %}
    """
  )
}}

SELECT
    provider,
    tariff_name,
    price,
    highlight,
    grade,
    experiment_filename,
    experiment_file_md5_hash,
    experiment_name,
    project_name,
    tariff,
    index,
    url,
    status,
    status_reason,
    CAST(crawler_run_date AS DATE)                              AS crawler_run_date,
    '{{ var('iptiq_load_id', 'NULL') }}'                        AS iptiq_load_id,
    '{{ var('iptiq_load_date', 'NULL') }}'                      AS iptiq_load_date,
    '{{ var('iptiq_execution_date', 'NULL') }}'                 AS iptiq_execution_date
FROM {{ source('databucket_raw', 'check24_building_insurance_pricing_data') }}
WHERE
    crawler_run_date = '{{ var('crawler_run_date', "'1900-01-01'") }}'
