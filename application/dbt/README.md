# Price Crawler dbt Project

Transforms raw pricing data from insurance comparison websites into structured datasets using Amazon Athena and Iceberg tables.

## Local Setup

### Automated Setup (Recommended)
```bash
cd application/dbt
aws-valut exec iptiq-dev
./setup_local_dev.sh
source activate_dbt.sh
```

### Manual Setup
```bash
cd application/dbt
python3 -m venv dbt_env && source dbt_env/bin/activate
pip install -r requirements.txt && dbt deps
```

### Activate AWS Profile if not yet activated
```bash
aws-valut exec iptiq-dev
```
## Environment Variables

Set these required variables:
```bash
export DBT_S3_STAGING_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/athena_query_results/dataengineering_workgroup/price_crawler/dbt_temp
export DBT_S3_DATA_DIR=s3://eu-central-1-912399619264-dev-datalake-infra-base-data/target/price_crawler/dbt
export DBT_ATHENA_WORKGROUP=data_engineering_workgroup
export USER=your_username # (ex:dinesh_nallabothula)
export DBT_S3_ACL=bucket-owner-full-control
export DBT_S3_SSEKMSID='acb2aaab-65c0-4b73-866a-457796a8fc34'
export DBT_S3_SERVER_SIDE_ENCRYPTION=aws:kms
```

## Test Connection
```bash
dbt debug
```

## Essential Commands

### Run Models
Once they are set you can execute dbt using your personal schema information:

```bash
dbt run --profile personal_schema
```

```bash
dbt run --profile personal_schema                                  # Run all models
dbt run --select model_name --profile personal_schema              # Run specific model
dbt run --select +model_name+ --profile personal_schema            # Run with dependencies
dbt run --full-refresh --profile personal_schema                   # Full refresh incremental models
```

### Test & Documentation
```bash
dbt test --profile personal_schema                                   # Run all tests
dbt test --select model_name --profile personal_schema               # Test specific model
dbt docs generate && dbt docs serve --profile personal_schema        # Generate and serve docs
```

### With Variables
```bash
dbt run --select model_name --vars '{"crawler_run_date": "2024-01-15"}' --profile personal_schema
```

## Available Models

### Staging Models
- `stg_comparis_motor_insurance_pricing_data` - Comparis motor insurance data
- `stg_check24_building_insurance_pricing_data` - Check24 building insurance data
- `stg_check24_content_insurance_pricing_data` - Check24 content insurance data

All models use Iceberg tables partitioned by `crawler_run_date`.

## Troubleshooting

### Connection Issues
```bash
aws sts get-caller-identity    # Check AWS credentials
dbt debug                      # Test connection
```

### Common Fixes
```bash
dbt clean && dbt deps                                   # Reinstall packages
dbt compile --select model --profile personal_schema    # Check syntax
dbt run --full-refresh --profile personal_schema         # Rebuild incremental models
```

### Debug Commands
```bash
dbt list --select +model+ --profile personal_schema     # Show dependencies
tail -f logs/dbt.log          # Monitor logs
```

## Resources
- [Macros Documentation](macros/README.md) - Custom macros
