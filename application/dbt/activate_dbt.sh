#!/bin/bash
# Activate dbt environment and load environment variables

if [ -f "dbt_env/bin/activate" ]; then
    source dbt_env/bin/activate
else
    echo "Error: Virtual environment 'dbt_env' not found. Create it first with 'python3 -m venv dbt_env'." >&2
    exit 1
fi
if [ -f ".env" ]; then
    source .env
    echo "dbt environment activated with environment variables loaded"
else
    echo "dbt environment activated (no .env file found)"
fi

echo "dbt version: $(dbt --version | head -n2)"
