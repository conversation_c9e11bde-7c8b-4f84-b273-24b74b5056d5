"""
DataFrame manipulation utilities.

This module provides functions for common DataFrame operations including
type conversions, value mappings, and conditional calculations.
"""

import logging
from typing import Any, Dict, List, Union

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


def convert_columns_to_integers(df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
    """
    Convert specified DataFrame columns to integers.

    Args:
        df: Input DataFrame
        columns: List of column names to convert

    Returns:
        DataFrame with specified columns converted to integers

    Raises:
        ValueError: If conversion fails for any column

    Example:
        >>> df = pd.DataFrame({'age': ['25.0', '30.0'], 'id': ['1', '2']})
        >>> convert_columns_to_integers(df, ['age', 'id'])
    """
    df_copy = df.copy()
    try:
        df_copy[columns] = df_copy[columns].astype(float).astype(int)
        return df_copy
    except (ValueError, TypeError) as e:
        logger.error(f"Failed to convert columns {columns} to integers: {str(e)}")
        raise


def apply_value_mappings(df: pd.DataFrame, column_mapping_dict: Dict[str, Dict[Any, Any]]) -> pd.DataFrame:
    """
    Apply value mappings to DataFrame columns.

    Args:
        df: Input DataFrame
        column_mapping_dict: Dictionary mapping column names to value mappings

    Returns:
        DataFrame with mapped values

    Example:
        >>> mappings = {'status': {'A': 'Active', 'I': 'Inactive'}}
        >>> apply_value_mappings(df, mappings)
    """
    df_copy = df.copy()
    df_copy.replace(column_mapping_dict, inplace=True)
    return df_copy


def calculate_conditional_values(conditions: List[np.ndarray], values: List[Any], default: Any = None) -> np.ndarray:
    """
    Calculate values based on conditions using numpy.select.

    Args:
        conditions: List of boolean arrays for conditions
        values: List of values corresponding to conditions
        default: Default value if no condition is met

    Returns:
        Array of calculated values based on conditions

    Example:
        >>> conditions = [df['age'] < 18, df['age'] < 65]
        >>> values = ['minor', 'adult']
        >>> df['category'] = calculate_conditional_values(conditions, values, 'senior')
    """
    return np.select(conditions, values, default)
