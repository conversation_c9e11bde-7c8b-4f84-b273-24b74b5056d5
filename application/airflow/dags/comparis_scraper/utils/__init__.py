"""
Common Utility Functions for Comparis Scraper

This package provides core utility functions used across the Comparis scraper modules,
organized by functionality:

- date_utils: Date formatting and manipulation
- dataframe_utils: DataFrame manipulation and transformation
- string_utils: String manipulation utilities
- model_utils: Pydantic model utilities for request/response handling
- validation_utils: Data validation for API interactions
- data_processing: Data manipulation and analysis utilities

Most commonly used functions are exposed at the package level for convenience.
"""

from .data_processing import calculate_success_rate, save_results_to_csv

# Import commonly used functions to make them available directly
from .dataframe_utils import apply_value_mappings, calculate_conditional_values, convert_columns_to_integers
from .date_utils import calculate_date_from_age, format_date_string, format_date_to_iso
from .model_utils import create_model_instance
from .string_utils import to_snake_case
from .validation_utils import validate_model_data, validate_required_columns

# Define what's available when using "from comparis_scraper.utils import *"
__all__ = [
    # Date utilities
    "format_date_to_iso",
    "format_date_string",
    "calculate_date_from_age",
    # DataFrame utilities
    "convert_columns_to_integers",
    "apply_value_mappings",
    "calculate_conditional_values",
    # String utilities
    "to_snake_case",
    # Model utilities
    "create_model_instance",
    # Validation utilities
    "validate_required_columns",
    "validate_model_data",
    # Data processing utilities
    "calculate_success_rate",
    "save_results_to_csv",
]
