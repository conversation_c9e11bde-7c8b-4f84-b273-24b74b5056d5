import asyncio
import logging
import uuid
from datetime import date, datetime
from functools import partial
from pathlib import Path
from typing import Dict

import httpx
from check24_crawlers.check24_content_scrapper import (
    Check24ContentInsuranceScraper,
    Check24ContentInsuranceScraperArguments,
)
from kubernetes.client import models as k8s
from quantum_data_pipeline.airflow.notifications import on_failure_slack, send_message
from quantum_data_pipeline.notifications.slack import SlackNotification
from quantum_data_pipeline.utils.common import (
    DATALAKE_ENVIRONMENT,
    connect_to_aws_dev_from_local,
    get_execution_date,
    get_ingestion_date,
    is_prod,
)
from slack_sdk.webhook import WebhookClient

from airflow.decorators import dag, task
from airflow.hooks.base import BaseHook
from airflow.models import Variable

logger = logging.getLogger(__file__)

slack_connection = BaseHook.get_connection("slack_alarm")
on_failure = partial(
    on_failure_slack,
    slack_channel_url=f"{slack_connection.host}{slack_connection.password}",
)

pod_override = {
    "pod_override": k8s.V1Pod(
        metadata=k8s.V1ObjectMeta(labels={"purpose": "pod-override-check24-content-crawler-dag-resources"}),
        spec=k8s.V1PodSpec(
            containers=[
                k8s.V1Container(
                    name="base",
                    resources=k8s.V1ResourceRequirements(limits={"cpu": "1.5", "memory": "6Gi"}),
                )
            ]
        ),
    )
}


default_args = {
    "owner": "<EMAIL>",
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "on_failure_callback": on_failure,
}


@dag(
    description="Scrap data from check24 content insurance comparison website",
    default_args=default_args,
    schedule="0 4 * * MON" if is_prod() else None,
    start_date=datetime(2024, 1, 28),
    tags=["Check24", "Crawlers", "Content"],
    catchup=False,
)
def dag_check24_content_insurance_pricing_data_extractor():
    @task
    def load_input_parameters_to_dropzone() -> Dict[str, str]:
        """Load input parameters to dropzone if there are changes to the file content"""
        import pandas as pd
        from quantum_data_pipeline.common.utils import create_md5hex, get_by_path
        from quantum_data_pipeline.sink import sink_factory
        from quantum_data_pipeline.sink.s3 import S3Sink
        from quantum_data_pipeline.transform.base import BaseTransformer
        from quantum_data_pipeline.transform.pandas import add_cols

        from airflow.utils.file import open_maybe_zipped

        load_id = str(uuid.uuid4())
        execution_date: str = get_execution_date()
        load_date = str(date.today())
        ingestion_date: str = get_ingestion_date()

        platform_config: Dict = Variable.get("platform", deserialize_json=True)
        dag_config: Dict = Variable.get("check24_content_insurance_scrapper", deserialize_json=True)

        connect_to_aws_dev_from_local()
        sink_conf = {
            "type": "s3",
            "bucket": platform_config.get("data_bucket"),
            "output_format": "csv",
            "s3_client_kwargs": {"ACL": "bucket-owner-full-control"},
        }
        sink: S3Sink

        csv_path = Path(__file__).parent / get_by_path(dag_config, "input_parameters_path")
        with open_maybe_zipped(str(csv_path), "r") as file:
            md5 = create_md5hex(file.read().encode("utf-8"))
            logger.info(f"{str(csv_path)} file md5: {md5}")

        athena_input_parameters_table = "check24_content_input_parameters"
        target_s3_prefix = f"drop_zone/crawlers/check24/content/{athena_input_parameters_table}"
        with sink_factory(environment=DATALAKE_ENVIRONMENT, **sink_conf) as sink:
            files_list = sink.list_objects(prefix=target_s3_prefix)
            target_s3_filename = f"{target_s3_prefix}/check24_content_basket_{md5}.csv"
            if target_s3_filename in files_list:
                return {"load_status": "skip", "md5hash": md5}

        load_attrs = {
            "iptiq_load_id": load_id,
            "iptiq_load_source": "Check24 Content Insurance Website Input Parameters from Pricing Team",
            "iptiq_load_date": load_date,
            "iptiq_execution_date": execution_date,
            "ingestion_date": ingestion_date,
            "experiment_file_md5_hash": md5,
            "experiment_filename": str(csv_path).split("/")[-1],
        }
        with open_maybe_zipped(str(csv_path), "r") as file:
            df_prams = pd.read_csv(file)
            scrapper = Check24ContentInsuranceScraper()
            df_prams = scrapper.df_transformation(df_prams)
            transformer = BaseTransformer(streaming=False)
            transformer.add(add_cols, cols=load_attrs)
            sink.write_data(key=target_s3_filename, data=transformer.process(df_prams), load_attrs=load_attrs)
            return {"load_status": "load", "md5hash": md5}

    @task(executor_config=pod_override, on_failure_callback=on_failure)
    def to_raw(task_args: Dict[str, str]) -> str:
        """Extracts content pricing data from check24 comparison website and load to athena"""
        load_input_parameters = task_args["load_status"]
        md5hash = task_args["md5hash"]
        connect_to_aws_dev_from_local()
        partition_date = datetime.today().strftime("%Y-%m-%d")

        async def scrap_pricing_data():
            from quantum_data_pipeline.common.utils import get_by_path
            from quantum_data_pipeline.configuration.pipeline_configuration import AthenaConf
            from quantum_data_pipeline.sink import sink_factory
            from quantum_data_pipeline.sink.athena import AthenaSink
            from quantum_data_pipeline.source import source_factory
            from quantum_data_pipeline.source.s3 import S3Source
            from quantum_data_pipeline.transform.base import BaseTransformer
            from quantum_data_pipeline.transform.pandas import add_cols

            load_id = str(uuid.uuid4())
            execution_date: str = get_execution_date()
            load_date = str(date.today())
            ingestion_date: str = get_ingestion_date()

            platform_config: Dict = Variable.get("platform", deserialize_json=True)
            dag_config: Dict = Variable.get("check24_content_insurance_scrapper", deserialize_json=True)
            data_bucket = get_by_path(platform_config, "data_bucket")
            kms_key_id_path = get_by_path(dag_config, "athena_sink.athena_kms_key_id")

            # create config objects
            athena_db = get_by_path(dag_config, "athena_sink.raw_database")
            athena_conf = AthenaConf(bucket=data_bucket, kms_key_id_path=kms_key_id_path)
            sink: AthenaSink
            if load_input_parameters == "load":
                source_conf = {"type": "s3", "bucket": platform_config.get("data_bucket")}
                source: S3Source
                athena_input_parameters_table = "check24_content_input_parameters"
                athena_sink_conf = athena_conf.to_athena_sink_conf(
                    athena_database=athena_db, athena_table=athena_input_parameters_table
                )
                sink: AthenaSink
                with source_factory(DATALAKE_ENVIRONMENT, **source_conf) as source:
                    with sink_factory(DATALAKE_ENVIRONMENT, **athena_sink_conf) as sink:
                        df_prams = source.read_streaming_data(
                            key=f"drop_zone/crawlers/check24/content/{athena_input_parameters_table}/"
                        )
                        target_key = (
                            "s3://"
                            + athena_sink_conf["bucket"]
                            + "/".join(["/raw", "crawlers", "check24", "content/"])
                            + athena_input_parameters_table
                        )
                        sink.write_streaming_data(path=target_key, data=df_prams, output_format="parquet")

            load_attrs = {
                "iptiq_load_id": load_id,
                "iptiq_load_source": "Check24 Content Scraper Website",
                "iptiq_load_date": load_date,
                "iptiq_execution_date": execution_date,
                "iptiq_ingestion_date": ingestion_date,
            }

            transformer = BaseTransformer(streaming=False)
            transformer.add(add_cols, cols=load_attrs)

            # Get CSV path and create arguments instance
            csv_path = Path(__file__).parent / get_by_path(dag_config, "input_parameters_path")
            requests_batch_size = get_by_path(dag_config, "requests_batch_size")
            args_instance = Check24ContentInsuranceScraperArguments(
                csv=csv_path, chunk_size=requests_batch_size, md5_hash=md5hash
            )

            # Set up a semaphore to limit concurrent requests
            semaphore: asyncio.Semaphore = asyncio.Semaphore(get_by_path(dag_config, "max_concurrent_requests"))
            max_retry_count = get_by_path(dag_config, "max_retry_count")
            html_s3_prefix = get_by_path(dag_config, "html_s3_prefix")
            scrapper = Check24ContentInsuranceScraper(
                semaphore=semaphore, max_retries=max_retry_count, bucket_name=data_bucket, html_s3_prefix=html_s3_prefix
            )

            athena_table = get_by_path(dag_config, "athena_sink.table")
            athena_sink_conf = athena_conf.to_athena_sink_conf(athena_database=athena_db, athena_table=athena_table)
            target_key = (
                "s3://"
                + athena_sink_conf["bucket"]
                + "/".join(["/raw", "crawlers", "check24", "content/"])
                + athena_sink_conf["table"]
            )
            partitions = {"crawler_run_date": partition_date}
            logger.info(f"Writing to {target_key}..")
            no_of_files = 0
            async with httpx.AsyncClient() as session:
                with sink_factory(DATALAKE_ENVIRONMENT, **athena_sink_conf) as sink:
                    async for data_df in scrapper.process_parameters(session=session, args=args_instance):
                        sink.write_data(
                            path=target_key,
                            data=transformer.process(data_df),
                            partition_cols=partitions,
                            mode="append" if no_of_files else "overwrite_partitions",
                        )
                        no_of_files += 1
                        logger.info(f"Number of requests completed so far:{no_of_files * requests_batch_size}....")

        def wrapper(loop):
            return loop.run_until_complete(scrap_pricing_data())

        wrapper(asyncio.get_event_loop())
        return partition_date

    @task
    def run_dbt(partition_date: str) -> None:
        import os
        import sys

        # Add the current directory to the path to find helpers module
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        from helpers.common import run_dbt_command

        dbt_cmd = "run --select stg_check24_content_insurance_pricing_data"
        run_dbt_command(partition_date=partition_date, dbt_cmd=dbt_cmd)

    @task(trigger_rule="none_failed")
    def send_notifications() -> None:
        """Send notifications"""
        if DATALAKE_ENVIRONMENT == "local":
            logger.info("Skipping notifications for local environment")
            return

        slack_connection = BaseHook.get_connection("slack_notification")
        sender = SlackNotification(
            WebhookClient(url=f"{slack_connection.host}{slack_connection.password}"),
            environment=DATALAKE_ENVIRONMENT,
        )

        send_message(
            sender=sender,
            msg=""":large_green_circle: Pipeline finished successfully.""",
        )

    task_params = load_input_parameters_to_dropzone()
    partition_date = to_raw(task_params)
    run_dbt(partition_date) >> send_notifications()


data = dag_check24_content_insurance_pricing_data_extractor()
