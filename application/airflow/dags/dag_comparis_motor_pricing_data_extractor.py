import asyncio
import base64
import logging
import os
import uuid
from datetime import date, datetime
from functools import partial
from pathlib import Path
from typing import Any, Dict, <PERSON><PERSON>

import httpx
from comparis_scraper.models.comparis_motor_insurance_request import ComparisMotorScraperArguments
from comparis_scraper.scrapers.comparis_motor_insurance_scraper import ComparisMotorInsuranceScraper
from quantum_data_pipeline.airflow.notifications import on_failure_slack, send_message
from quantum_data_pipeline.notifications.slack import SlackNotification
from quantum_data_pipeline.utils.common import (
    DATALAKE_ENVIRONMENT,
    connect_to_aws_dev_from_local,
    get_execution_date,
    get_ingestion_date,
    is_prod,
    read_yaml,
)
from slack_sdk.webhook import WebhookClient

from airflow.decorators import dag, task
from airflow.hooks.base import BaseHook
from airflow.models import Variable

logger = logging.getLogger(__file__)

slack_connection = BaseHook.get_connection("slack_alarm")
on_failure = partial(
    on_failure_slack,
    slack_channel_url=f"{slack_connection.host}{slack_connection.password}",
)

CHMOTOR_CRAWLER_OWNERS = ",".join(["<EMAIL>", "Backup: <EMAIL>"])
dag_doc = """
    # Comparis Motor Insurance Pricing Data Extractor

    This DAG extracts motor insurance pricing data from the Comparis website.

    ## Process Flow
    1. Load input parameters to dropzone
    2. Extract data from Comparis website
    3. Load data to raw layer
    4. Transform and Process data to target layer
    5. Send notifications

    ## Owner
    Primary: <EMAIL>
    Backup: <EMAIL>

    ## Schedule
    Monthly on the 5th at 01:00 UTC
    """

default_args = {
    "owner": CHMOTOR_CRAWLER_OWNERS,
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "on_failure_callback": on_failure,
}


def get_table_definitions(root_key: str) -> Tuple[Any]:
    """Reads source configuration and returns schema and table definitions"""
    definition_path = "athena_schemas/comparis/comparis_crawlers_schema.yml"
    absolute_definition_path = os.path.join(os.path.dirname(__file__), definition_path)
    data_types = read_yaml(absolute_definition_path)
    return data_types[root_key]["schema"]


@dag(
    description="Scrape pricing data from Comparis motor insurance comparison website",
    default_args=default_args,
    schedule="0 21 5 * *",
    start_date=datetime(2025, 2, 26),
    tags=["Comparis", "Crawlers", "CHMotor", "Web Scraping", "Insurance"],
    catchup=False,
    doc_md=dag_doc,
    max_active_runs=1,
)
def dag_comparis_motor_insurance_pricing_data_extractor():
    @task
    def load_input_parameters_to_dropzone() -> Dict[str, str]:
        """Load input parameters to dropzone if there are changes to the file content"""
        import pandas as pd
        from quantum_data_pipeline.common.utils import create_md5hex, get_by_path
        from quantum_data_pipeline.sink import sink_factory
        from quantum_data_pipeline.sink.s3 import S3Sink
        from quantum_data_pipeline.transform.base import BaseTransformer
        from quantum_data_pipeline.transform.pandas import add_cols

        from airflow.utils.file import open_maybe_zipped

        load_id = str(uuid.uuid4())
        execution_date: str = get_execution_date()
        load_date = str(date.today())
        ingestion_date: str = get_ingestion_date()

        platform_config: Dict = Variable.get("platform", deserialize_json=True)
        dag_config: Dict = Variable.get("comparis_motor_insurance_scraper", deserialize_json=True)

        connect_to_aws_dev_from_local()

        csv_path = Path(__file__).parent / get_by_path(dag_config, "input_parameters_path")
        with open_maybe_zipped(str(csv_path), "r") as file:
            md5 = create_md5hex(file.read().encode("utf-8"))
            logger.info(f"{str(csv_path)} file md5: {md5}")

        sink_conf = {
            "type": "s3",
            "bucket": platform_config.get("data_bucket"),
            "output_format": "csv",
            "s3_client_kwargs": {"ACL": "bucket-owner-full-control"},
        }
        sink: S3Sink

        athena_input_parameters_table = "comparis_motor_input_parameters"
        target_s3_prefix = f"drop_zone/crawlers/comparis/motor/{athena_input_parameters_table}"

        with sink_factory(environment=DATALAKE_ENVIRONMENT, **sink_conf) as sink:
            files_list = sink.list_objects(prefix=target_s3_prefix)
            target_s3_filename = f"{target_s3_prefix}/comparis_motor_input_parameters_{md5}.csv"
            if target_s3_filename in files_list:
                return {"load_status": "skip", "md5hash": md5}

        load_attrs = {
            "iptiq_load_id": load_id,
            "iptiq_load_source": "Comparis Motor Insurance Website Input Parameters from pricing team",
            "iptiq_load_date": load_date,
            "iptiq_execution_date": execution_date,
            "ingestion_date": ingestion_date,
            "experiment_file_md5_hash": md5,
            "experiment_filename": str(csv_path).split("/")[-1],
        }

        with open_maybe_zipped(str(csv_path), "r") as file:
            df_params = pd.read_csv(file)
            transformer = BaseTransformer(streaming=False)
            transformer.add(add_cols, cols=load_attrs)
            sink.write_data(key=target_s3_filename, data=transformer.process(df_params), load_attrs=load_attrs)
            return {"load_status": "load", "md5hash": md5}

    @task(on_failure_callback=on_failure)
    def to_raw(task_args: Dict[str, str]) -> str:
        """Extract motor insurance pricing data from Comparis and load to Athena"""
        from comparis_scraper.utils.data_processing import calculate_success_rate

        from airflow.exceptions import AirflowFailException

        load_input_parameters = task_args["load_status"]
        md5hash = task_args["md5hash"]
        connect_to_aws_dev_from_local()
        partition_date = datetime.today().strftime("%Y-%m-%d")

        async def scrape_pricing_data():
            import json
            import tempfile

            from quantum_data_pipeline.common.utils import get_by_path
            from quantum_data_pipeline.configuration.pipeline_configuration import AthenaConf
            from quantum_data_pipeline.sink import sink_factory
            from quantum_data_pipeline.sink.athena import AthenaSink
            from quantum_data_pipeline.source import source_factory
            from quantum_data_pipeline.source.s3 import S3Source
            from quantum_data_pipeline.transform.base import BaseTransformer
            from quantum_data_pipeline.transform.pandas import add_cols

            platform_config: Dict = Variable.get("platform", deserialize_json=True)
            dag_config: Dict = Variable.get("comparis_motor_insurance_scraper", deserialize_json=True)
            data_bucket = get_by_path(platform_config, "data_bucket")
            kms_key_id_path = get_by_path(dag_config, "athena_sink.athena_kms_key_id")

            # create config objects
            athena_db = get_by_path(dag_config, "athena_sink.raw_database")
            athena_conf = AthenaConf(bucket=data_bucket, kms_key_id_path=kms_key_id_path)
            sink: AthenaSink
            if load_input_parameters == "load":
                source_conf = {"type": "s3", "bucket": platform_config.get("data_bucket")}
                source: S3Source
                athena_input_parameters_table = "comparis_motor_input_parameters"
                athena_sink_conf = athena_conf.to_athena_sink_conf(
                    athena_database=athena_db, athena_table=athena_input_parameters_table
                )
                sink: AthenaSink
                with source_factory(DATALAKE_ENVIRONMENT, **source_conf) as source:
                    with sink_factory(DATALAKE_ENVIRONMENT, **athena_sink_conf) as sink:
                        df_prams = source.read_streaming_data(
                            key=f"drop_zone/crawlers/comparis/motor/{athena_input_parameters_table}/"
                        )
                        target_key = (
                            "s3://"
                            + athena_sink_conf["bucket"]
                            + "/".join(["/raw", "crawlers", "comparis", "motor/"])
                            + athena_input_parameters_table
                        )
                        sink.write_streaming_data(path=target_key, data=df_prams, output_format="parquet")

            load_attrs = {
                "iptiq_load_id": str(uuid.uuid4()),
                "iptiq_load_source": "Comparis Motor Insurance Website",
                "iptiq_load_date": str(date.today()),
                "iptiq_execution_date": get_execution_date(),
                "iptiq_ingestion_date": get_ingestion_date(),
            }
            sink: AthenaSink
            transformer = BaseTransformer(streaming=False)
            transformer.add(add_cols, cols=load_attrs)

            # Configure Athena sink
            athena_table = get_by_path(dag_config, "athena_sink.table")
            athena_sink_conf = athena_conf.to_athena_sink_conf(athena_database=athena_db, athena_table=athena_table)

            target_key = f"s3://{athena_sink_conf['bucket']}/raw/crawlers/comparis/motor/{athena_sink_conf['table']}"
            partitions = {"crawler_run_date": partition_date}

            no_of_files = 0
            conn = BaseHook.get_connection("bright_data_webscraper")
            # Get proxy details
            # gitlab-secret-detection-ignore: this is a properly managed Airflow connection
            proxy_url = f"http://{conn.login}:{conn.password}@{conn.host}:{conn.port}"  # gitleaks:allow
            proxy_config = {"http": proxy_url, "https": proxy_url}
            # Ensure proxy keys use proper URL forms
            proxy_config = {f"{scheme}://": url for scheme, url in proxy_config.items()}
            # Get SSL certificate from extra
            extra = json.loads(conn.extra) if conn.extra else {}
            cert_content_base64 = extra.get("ssl_certificate")

            # Create temporary file for certificate if it exists
            cert_path = None
            if cert_content_base64:
                cert_content = base64.b64decode(cert_content_base64, validate=True).decode("utf-8")

                with tempfile.NamedTemporaryFile(suffix=".crt", delete=False) as cert_file:
                    cert_file.write(cert_content.encode())
                    cert_path = cert_file.name
            try:
                async with httpx.AsyncClient(proxies=proxy_config, verify=cert_path) as session:
                    with sink_factory(DATALAKE_ENVIRONMENT, **athena_sink_conf) as sink:
                        # Initialize scraper
                        csv_path = Path(__file__).parent / get_by_path(dag_config, "input_parameters_path")
                        semaphore = asyncio.Semaphore(get_by_path(dag_config, "max_concurrent_requests"))
                        scraper = ComparisMotorInsuranceScraper(
                            session=session, semaphore=semaphore, max_retries=get_by_path(dag_config, "max_retries")
                        )
                        args = ComparisMotorScraperArguments(
                            csv=Path(csv_path), chunk_size=get_by_path(dag_config, "chunk_size"), md5_hash=md5hash
                        )
                        dtypes = dict(get_table_definitions(athena_table))
                        logger.info(f"dtypes:{dtypes}\n")
                        async for data_df in scraper.crawl_and_process_data(args):
                            sink.write_data(
                                path=target_key,
                                data=transformer.process(data_df),
                                partition_cols=partitions,
                                mode="append" if no_of_files else "overwrite_partitions",
                                dtypes=dtypes,
                            )
                            no_of_files += 1
                            logger.info(f"Number of requests completed: {no_of_files * len(data_df)}")
                            success_rate, success_count, total_count = calculate_success_rate(data_df)
                            logger.info(f"Success rate: {success_rate:.2f}% ({success_count}/{total_count})")
                            if success_rate < 90:
                                raise AirflowFailException(
                                    f"Crawl quality threshold not met: {100 - success_rate:.2f}% error rate exceeds the 10% limit "
                                    f"({total_count - success_count} failures out of {total_count} requests). "
                                    f"Please check the error field in Athena table '{athena_table}' with "
                                    f"crawler_run_date='{partition_date}' for details. "
                                    f"Contact {CHMOTOR_CRAWLER_OWNERS} for assistance if needed."
                                )
            finally:
                # Clean up the temporary certificate file
                if cert_path and os.path.exists(cert_path):
                    os.unlink(cert_path)

        asyncio.run(scrape_pricing_data())
        return partition_date

    @task
    def run_dbt(partition_date: str) -> None:
        import os
        import sys

        # Add the current directory to the path to find helpers module
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        from helpers.common import run_dbt_command

        dbt_cmd = "run --select stg_comparis_motor_insurance_pricing_data"
        run_dbt_command(partition_date=partition_date, dbt_cmd=dbt_cmd)

    @task(trigger_rule="none_failed")
    def send_notifications() -> None:
        """Send notifications"""
        if DATALAKE_ENVIRONMENT == "local":
            logger.info("Skipping notifications for local environment")
            return

        slack_connection = BaseHook.get_connection("slack_notification")
        sender = SlackNotification(
            WebhookClient(url=f"{slack_connection.host}{slack_connection.password}"),
            environment=DATALAKE_ENVIRONMENT,
        )

        send_message(
            sender=sender,
            msg=""":large_green_circle: Pipeline finished successfully.""",
        )

    task_params = load_input_parameters_to_dropzone()
    partition_date = to_raw(task_params)
    run_dbt(partition_date) >> send_notifications()


dag = dag_comparis_motor_insurance_pricing_data_extractor()
