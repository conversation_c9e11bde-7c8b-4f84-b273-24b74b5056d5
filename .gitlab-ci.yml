include:
  - project: 'iptiq/data/ci-cd-includes'
    ref: 0.0.18
    file: '/ci-cd-templates.yml'

variables:
  extends: .cicd-include-variables
  PROJECT: ${CI_PROJECT_NAME}
  AIRFLOW_NAMESPACE: "airflow"
  DAGS_BUCKET: '${AWS_REGION}-${DATALAKE_AWS_ACCOUNT_ID}-${ENVIRONMENT_SHORT}-${PLATFORM}-airflow-dags'
  AIRFLOW_ARTIFACT_NAME: 'zip_dags_${CI_PROJECT_NAME}'
  AIRFLOW_ARTIFACT_NAME_EXT: "${AIRFLOW_ARTIFACT_NAME}.zip"
  DBT_FOLDER: dbt
  DBT_PATH: "$CI_PROJECT_DIR/application/$DBT_FOLDER/"
  DBT_S3_STAGING_DIR: "s3://${AWS_REGION}-${DATAL<PERSON><PERSON>_AWS_ACCOUNT_ID}-${ENVIRONMENT_SHORT}-datalake-infra-base-data/athena_query_results/dataengineering_workgroup/price_crawler/dbt_temp"
  DBT_S3_DATA_DIR: "s3://${AWS_REGION}-${DATALAKE_AWS_ACCOUNT_ID}-${ENVIRONMENT_SHORT}-datalake-infra-base-data/target/price_crawler/dbt"
  DBT_DOCS_BUCKET: "${AWS_REGION}-${DATALAKE_AWS_ACCOUNT_ID}-${ENVIRONMENT_SHORT}-${PLATFORM}-dbt-model-docs"

default:
  image: "$BUILD_IMAGE"
  tags:
    - shared-services
  before_script:
    - !reference [ .AWS_LOG_IN, before_script ]

stages:
  - manual-trigger
  - pre-commit-check
  - static-analysis
  - build
  - test
  - deploy_services

dbt_compile_and_unit_test:
  stage: build
  before_script:
    - echo "dbt compile and generate docs "
    - export USER="gitlab_pipeline"
    - !reference [ .AWS_LOG_IN, before_script ]
  script:
    - cd $DBT_PATH
    - pip install -r requirements.txt
    - dbt --version
    - dbt deps
    - dbt compile
    - mkdir -p $AIRFLOW_PATH/dags/dbt_config/
    - cp $DBT_PATH/target/manifest.json $AIRFLOW_PATH/dags/dbt_config/
    # we don't have any tests yet
    - dbt test --model tag:unit-test tag:integration-test
    - |
      if [ "$ENVIRONMENT_SHORT" != "stg" ]; then
        dbt docs generate
        aws s3 sync $CI_PROJECT_DIR/application/dbt/target/ s3://${DBT_DOCS_BUCKET}/price_crawler/ --delete --exact-timestamps --acl=bucket-owner-full-control
      else
        echo "Skipping dbt docs generation and S3 sync for staging environment"
      fi
  artifacts:
    name: "$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"
    expire_in: 1 hour
    paths:
      - $AIRFLOW_PATH/dags/dbt_config/
    when: on_success

airflow_build_artifacts:
  stage: build
  environment:
    name: ${APPLICATION_DEPLOYMENT_ENVIRONMENT}/airflow
  before_script:
    - export PIPELINE_VERSION=$(cat "PIPELINE_VERSION")
  script:
    - cd $CI_PROJECT_DIR/application/airflow/dags/
    - zip -r9 $CI_PROJECT_DIR/$AIRFLOW_ARTIFACT_NAME . -x '*__pycache__*' -x '*.pytest_cache*' -x '*.DS_Store'
    - pip install --no-deps quantum-data-pipeline==$PIPELINE_VERSION --extra-index-url https://gitlab-ci-token:$<EMAIL>/api/v4/projects/23534375/packages/pypi/simple -t . #gitleaks:allow
    - zip -r9 $CI_PROJECT_DIR/zip_dags_${CI_PROJECT_NAME} quantum_data_pipeline
  needs: ["dbt_compile_and_unit_test"]
  artifacts:
    name: "$CI_JOB_STAGE-${CI_COMMIT_REF_NAME}"
    expire_in: 1 hour
    paths:
      - $AIRFLOW_ARTIFACT_NAME_EXT
    when: on_success

gemnasium-python-dependency_scanning:
  before_script:
    - echo "Running dependency-scanning"
  needs: [ airflow_build_artifacts ]

deploy_airflow_service:
  stage: deploy_services
  environment:
    name: ${APPLICATION_DEPLOYMENT_ENVIRONMENT}/airflow
  script:
    - export PROJECT_PREFIX="/${ENVIRONMENT_SHORT}/${PLATFORM}/"
    - export BRIGHT_DATE_SSL_CERTIFICATE=$(aws ssm get-parameters --with-decryption --region ${AWS_REGION} --name "${PROJECT_PREFIX}brightdata/webscraper/proxy/ssl_certificate" | jq -r '.Parameters | first | .Value' | base64 -w 0 )
    - export BRIGHT_DATE_WEBSCRAPER_PWD=$(aws ssm get-parameters --with-decryption --region ${AWS_REGION} --name "${PROJECT_PREFIX}brightdata/webscraper/proxy/password" | jq -r '.Parameters | first | .Value' )
    - aws s3 rm s3://${DAGS_BUCKET}/dags/$AIRFLOW_ARTIFACT_NAME_EXT
    - aws s3 cp $CI_PROJECT_DIR/$AIRFLOW_ARTIFACT_NAME_EXT s3://${DAGS_BUCKET}/dags/ --acl=bucket-owner-full-control
    - cd $DBT_PATH
    - aws s3 sync . s3://${DAGS_BUCKET}/dags/${PROJECT}_${DBT_FOLDER} --exclude 'target/*' --exclude 'dbt_packages/*' --exclude Dockerfile --exclude '*.gitignore' --exclude 'logs/*' --exclude README.md --exclude .user.yml --delete --exact-timestamps --acl=bucket-owner-full-control
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_ID}
    - kubectl create namespace ${AIRFLOW_NAMESPACE} --dry-run -o yaml | kubectl apply -f -
    - $CI_PROJECT_DIR/scripts/deploy.sh airflow-cloud
